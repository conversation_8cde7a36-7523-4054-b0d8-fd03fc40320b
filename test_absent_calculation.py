# -*- coding: utf-8 -*-
"""
测试缺勤计算功能
验证修复后的缺勤统计是否正确
"""

from work_time_calculator import WorkTimeCalculator

def test_absent_calculation():
    """测试缺勤计算"""
    calculator = WorkTimeCalculator()
    
    # 模拟员工数据：31天中有5天没有打卡记录
    employee_data = {
        'id': 'TEST001',
        'daily_records': []
    }
    
    # 添加26天正常打卡记录
    for day in range(1, 27):
        employee_data['daily_records'].append({
            'day': day,
            'has_record': True,
            'is_abnormal': False,
            'work_start': '08:00',
            'work_end': '17:00',
            'raw_data': '08:00 17:00'
        })
    
    # 添加5天缺勤记录（没有打卡）
    for day in range(27, 32):
        employee_data['daily_records'].append({
            'day': day,
            'has_record': False,
            'is_abnormal': False,
            'work_start': None,
            'work_end': None,
            'raw_data': ''
        })
    
    # 测试场景1：没有假期天数
    print("=== 测试场景1：31天出勤，0天假期，5天缺勤 ===")
    result1 = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    summary1 = result1['monthly_summary']
    
    print(f"出勤天数: {summary1['total_work_days']}")
    print(f"缺勤天数: {summary1['absent_count']}")
    print(f"请假天数: {summary1['leave_count']}")
    print(f"违规总次数: {summary1['total_violations']}")
    print(f"是否满勤: {summary1['has_perfect_attendance']}")
    print()
    
    # 测试场景2：有2天假期
    print("=== 测试场景2：31天出勤，2天假期，5天缺勤 ===")
    result2 = calculator.calculate_employee_worktime(employee_data, holiday_days=2)
    summary2 = result2['monthly_summary']
    
    print(f"出勤天数: {summary2['total_work_days']}")
    print(f"缺勤天数: {summary2['absent_count']}")
    print(f"请假天数: {summary2['leave_count']}")
    print(f"违规总次数: {summary2['total_violations']}")
    print(f"是否满勤: {summary2['has_perfect_attendance']}")
    print()
    
    # 测试场景3：假期天数大于缺勤天数
    print("=== 测试场景3：31天出勤，10天假期，5天缺勤 ===")
    result3 = calculator.calculate_employee_worktime(employee_data, holiday_days=10)
    summary3 = result3['monthly_summary']
    
    print(f"出勤天数: {summary3['total_work_days']}")
    print(f"缺勤天数: {summary3['absent_count']}")
    print(f"请假天数: {summary3['leave_count']}")
    print(f"违规总次数: {summary3['total_violations']}")
    print(f"是否满勤: {summary3['has_perfect_attendance']}")
    print()
    
    # 验证结果
    print("=== 验证结果 ===")
    # 场景1：0天假期，5天没打卡 → 缺勤5天，请假5天
    assert summary1['absent_count'] == 5, f"场景1缺勤次数应为5，实际为{summary1['absent_count']}"
    assert summary1['leave_count'] == 5, f"场景1请假次数应为5，实际为{summary1['leave_count']}"

    # 场景2：2天假期，5天没打卡 → 缺勤3天，请假5天
    assert summary2['absent_count'] == 3, f"场景2缺勤次数应为3，实际为{summary2['absent_count']}"
    assert summary2['leave_count'] == 5, f"场景2请假次数应为5，实际为{summary2['leave_count']}"

    # 场景3：10天假期，5天没打卡 → 缺勤0天，请假5天
    assert summary3['absent_count'] == 0, f"场景3缺勤次数应为0，实际为{summary3['absent_count']}"
    assert summary3['leave_count'] == 5, f"场景3请假次数应为5，实际为{summary3['leave_count']}"

    print("✅ 所有测试通过！缺勤计算功能已按正确逻辑修复")
    print("📝 逻辑说明：")
    print("   - 请假次数 = 实际没打卡的天数")
    print("   - 缺勤次数 = max(0, 没打卡天数 - 规定假期天数)")
    print("   - 违规总次数 = 迟到次数 + 早退次数 + 缺勤次数")

if __name__ == "__main__":
    test_absent_calculation()
