# -*- coding: utf-8 -*-
"""
考勤工时计算系统打包脚本
使用PyInstaller将Python应用打包为exe文件
"""

import os
import sys
import subprocess
from pathlib import Path

def build_exe():
    """构建exe文件"""
    print("开始打包考勤工时计算系统...")
    
    # 当前目录
    current_dir = Path(__file__).parent
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包为单个exe文件
        '--windowed',                   # 不显示控制台窗口
        '--name=考勤工时计算系统',        # 指定exe文件名
        '--distpath=dist',              # 输出目录
        '--workpath=build',             # 临时文件目录
        '--specpath=.',                 # spec文件位置
        '--clean',                      # 清理临时文件
        '--noconfirm',                  # 不询问确认
        # 添加数据文件（如果有的话）
        # '--add-data=data;data',
        # 隐藏导入（解决一些模块找不到的问题）
        '--hidden-import=pandas',
        '--hidden-import=numpy',
        '--hidden-import=openpyxl',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        # 主程序文件
        'main.py'
    ]
    
    try:
        # 执行打包命令
        print("执行打包命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("打包成功！")
        print(f"输出文件位置: {current_dir / 'dist' / '考勤工时计算系统.exe'}")
        
        # 显示文件大小
        exe_path = current_dir / 'dist' / '考勤工时计算系统.exe'
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"文件大小: {size_mb:.1f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    except Exception as e:
        print(f"打包过程中发生错误: {e}")
        return False
    
    return True

def clean_build_files():
    """清理构建文件"""
    import shutil
    
    dirs_to_clean = ['build', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"已清理文件: {spec_file}")

if __name__ == "__main__":
    print("考勤工时计算系统 - 打包工具")
    print("=" * 50)
    
    # 检查PyInstaller是否安装
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到PyInstaller，请先安装:")
        print("pip install pyinstaller")
        sys.exit(1)
    
    # 执行打包
    success = build_exe()
    
    if success:
        print("\n打包完成！")
        print("可执行文件位置: dist/考勤工时计算系统.exe")
        
        # 询问是否清理构建文件
        response = input("\n是否清理构建临时文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            clean_build_files()
            print("构建文件已清理")
    else:
        print("\n打包失败，请检查错误信息")
