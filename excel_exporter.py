# -*- coding: utf-8 -*-
"""
Excel导出器
"""

import pandas as pd
from typing import Dict, List
import os


class ExcelExporter:
    """Excel导出器"""

    def __init__(self):
        pass

    def export_salary_report(self, calculation_results: Dict, file_path: str):
        """
        导出工资报表到Excel

        Args:
            calculation_results: 计算结果
            file_path: 导出文件路径
        """
        # 准备汇总数据
        summary_data = []
        detail_data = []

        for employee_id, result_data in calculation_results['results'].items():
            employee = result_data['employee']
            salary_result = result_data['salary_result']
            monthly_summary = salary_result['monthly_summary']
            base_salary = salary_result['base_salary']
            perfect_attendance = salary_result['perfect_attendance']

            # 汇总数据
            summary_data.append({
                '工号': employee['id'],
                '姓名': employee['name'],
                '部门': employee['department'],
                '出勤天数': monthly_summary['full_days'],
                '不满天工时': round(monthly_summary['remaining_hours'], 1),
                '正常工时总计': round(monthly_summary['total_normal_hours'], 1),
                '加班工时': round(monthly_summary['total_overtime_hours'], 1),
                '总工时': round(base_salary['total_work_hours'], 1),
                '迟到次数': monthly_summary['late_count'],
                '早退次数': monthly_summary['early_leave_count'],
                '请假次数': monthly_summary['absent_count'],
                '违规总次数': monthly_summary['total_violations'],
                '是否满勤': '是' if monthly_summary['has_perfect_attendance'] else '否',
                '时薪基数': round(base_salary['hourly_base_rate'], 4),
                '基础工资': round(base_salary['base_salary'], 2),
                '满勤奖': round(perfect_attendance['bonus_amount'], 2),
                '总工资': round(salary_result['total_salary'], 2)
            })

            # 详细数据 - 每日考勤
            daily_results = result_data['daily_results']
            for daily in daily_results:
                detail_data.append({
                    '工号': employee['id'],
                    '姓名': employee['name'],
                    '日期': f"5月{daily['day']}日",
                    '班次': '白班' if daily['shift_type'] == 'day' else '夜班',
                    '上班时间': daily['work_time'],
                    '下班时间': daily['off_time'],
                    '正常工时': round(daily['normal_hours'], 1),
                    '加班工时': round(daily['overtime_hours'], 1),
                    '是否迟到': '是' if daily['is_late'] else '否',
                    '迟到分钟': daily['late_minutes'],
                    '是否早退': '是' if daily['is_early_leave'] else '否',
                    '早退分钟': daily['early_leave_minutes']
                })

        # 创建Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 工资汇总表
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='工资汇总', index=False)

            # 考勤详细表
            detail_df = pd.DataFrame(detail_data)
            detail_df.to_excel(writer, sheet_name='考勤详细', index=False)

            # 计算参数表
            params_data = [
                ['参数名称', '参数值'],
                ['时薪', f"{calculation_results['calculation_params']['hourly_rate']} 元/小时"],
                ['当月天数', f"{calculation_results['calculation_params']['month_days']} 天"],
                ['假期天数', f"{calculation_results['calculation_params']['holiday_days']} 天"],
                ['满勤奖档位', f"第{calculation_results['calculation_params']['perfect_attendance_tier']}档"],
                ['', ''],
                ['计算规则说明', ''],
                ['白班时间', '7:30-17:00 (8.5小时)'],
                ['夜班时间', '17:00-次日01:00 (按8.5小时计算)'],
                ['工时单位', '15分钟'],
                ['加班阈值', '超过30分钟才计算'],
                ['满勤条件', '迟到+早退+请假 ≤ 3次'],
                ['工资公式', '(出勤天数×8.5h + 不满天工时 + 加班时间) × (工人工资÷30÷8.5)']
            ]

            params_df = pd.DataFrame(params_data)
            params_df.to_excel(writer, sheet_name='计算参数', index=False, header=False)

        return True

    def export_simple_salary_table(self, calculation_results: Dict, file_path: str):
        """
        导出简化工资表

        Args:
            calculation_results: 计算结果
            file_path: 导出文件路径
        """
        # 准备简化数据
        simple_data = []

        for employee_id, result_data in calculation_results['results'].items():
            employee = result_data['employee']
            salary_result = result_data['salary_result']
            monthly_summary = salary_result['monthly_summary']

            simple_data.append({
                '工号': employee['id'],
                '姓名': employee['name'],
                '出勤天数': f"{monthly_summary['full_days']}天{monthly_summary['remaining_hours']:.1f}小时",
                '加班工时': f"{monthly_summary['total_overtime_hours']:.1f}小时",
                '迟到次数': monthly_summary['late_count'],
                '早退次数': monthly_summary['early_leave_count'],
                '总工资': f"{salary_result['total_salary']:.2f}元"
            })

        # 创建DataFrame并保存
        df = pd.DataFrame(simple_data)
        df.to_excel(file_path, index=False, sheet_name='工资表')

        return True

    def export_worktime_report(self, calculation_results: Dict, file_path: str):
        """
        导出工时报表

        Args:
            calculation_results: 计算结果
            file_path: 导出文件路径
        """
        # 准备汇总数据
        summary_data = []
        detail_data = []

        for employee_id, result_data in calculation_results['results'].items():
            monthly_summary = result_data['monthly_summary']
            # 获取员工姓名
            employee_name = ''
            if 'employee_data' in result_data and 'name' in result_data['employee_data']:
                employee_name = result_data['employee_data']['name']

            # 汇总数据
            summary_data.append({
                '工号': employee_id,
                '姓名': employee_name,
                '出勤天数': monthly_summary['total_work_days'],
                '正常工时': round(monthly_summary['total_normal_hours'], 1),
                '加班工时': round(monthly_summary['total_overtime_hours'], 1),
                '总工时': round(monthly_summary['total_normal_hours'] + monthly_summary['total_overtime_hours'], 1),
                '迟到次数': monthly_summary['late_count'],
                '早退次数': monthly_summary['early_leave_count'],
                '缺勤次数': monthly_summary['absent_count'],
                '违规总次数': monthly_summary['total_violations'],
                '是否满勤': '是' if monthly_summary['has_perfect_attendance'] else '否'
            })

            # 详细数据 - 每日考勤
            daily_results = result_data['daily_results']
            for daily in daily_results:
                shift_text = ""
                if daily['shift_type'] == 'day':
                    shift_text = "白班"
                elif daily['shift_type'] == 'night':
                    shift_text = "夜班"

                detail_data.append({
                    '工号': employee_id,
                    '姓名': employee_name,
                    '日期': f"{daily['day']}日",
                    '班次': shift_text,
                    '上班时间': daily['work_start'] or '',
                    '下班时间': daily['work_end'] or '',
                    '总工时': round(daily['total_hours'], 1) if daily['total_hours'] > 0 else '',
                    '正常工时': round(daily['normal_hours'], 1) if daily['normal_hours'] > 0 else '',
                    '加班工时': round(daily['overtime_hours'], 1) if daily['overtime_hours'] > 0 else '',
                    '是否迟到': '是' if daily['is_late'] else '否',
                    '迟到分钟': daily['late_minutes'] if daily['is_late'] else '',
                    '是否早退': '是' if daily['is_early_leave'] else '否',
                    '早退分钟': daily['early_leave_minutes'] if daily['is_early_leave'] else '',
                    '是否出勤': '是' if daily['is_present'] else '否',
                    '原始数据': daily.get('raw_data', '')
                })

        # 创建Excel文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 工时汇总表
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='工时汇总', index=False)

            # 考勤详细表
            detail_df = pd.DataFrame(detail_data)
            detail_df.to_excel(writer, sheet_name='考勤详细', index=False)

            # 计算参数表
            params_data = [
                ['参数名称', '参数值'],
                ['总天数', f"{calculation_results['calculation_params']['total_days']} 天"],
                ['标准工时', f"{calculation_results['calculation_params']['standard_hours']} 小时/天"],
                ['时间单位', f"{calculation_results['calculation_params']['time_unit']} 分钟"],
                ['', ''],
                ['计算规则说明', ''],
                ['白班时间', '7:30-17:00 (8.5小时)'],
                ['夜班时间', '17:00-次日01:00 (按8.5小时计算)'],
                ['工时单位', '15分钟'],
                ['加班阈值', '超过30分钟才计算'],
                ['满勤条件', '迟到+早退+缺勤 ≤ 3次'],
                ['满勤奖档位', '第一档200元，第二档300元，第三档500元']
            ]

            params_df = pd.DataFrame(params_data)
            params_df.to_excel(writer, sheet_name='计算参数', index=False, header=False)

            # 如果有工资计算数据，添加工资表
            salary_data = []
            for employee_id, result_data in calculation_results['results'].items():
                if 'salary_calculation' in result_data:
                    salary_calc = result_data['salary_calculation']
                    tier = salary_calc['perfect_attendance_tier']
                    tier_text = f"{tier}档" if tier > 0 else "无"

                    salary_data.append({
                        '工号': employee_id,
                        '姓名': salary_calc['employee_name'],
                        '基础工资': salary_calc['base_salary'],
                        '工时费': salary_calc['hourly_rate'],
                        '出勤天数': f"{salary_calc['full_days']}天",
                        '不满一天工时': f"{salary_calc['remaining_hours']}小时",
                        '正常工时': f"{salary_calc['total_normal_hours']}小时",
                        '加班工时': f"{salary_calc['total_overtime_hours']}小时",
                        '总工时': f"{salary_calc['total_work_hours']}小时",
                        '基础工资计算': salary_calc['base_calculated_salary'],
                        '满勤档位': tier_text,
                        '满勤奖': salary_calc['perfect_attendance_bonus'],
                        '最终工资': salary_calc['calculated_salary'],
                        '工资差额': salary_calc['calculated_salary'] - salary_calc['base_salary']
                    })

            if salary_data:
                salary_df = pd.DataFrame(salary_data)
                salary_df.to_excel(writer, sheet_name='工资计算', index=False)

        return True
