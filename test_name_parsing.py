# -*- coding: utf-8 -*-
"""
测试姓名解析功能
验证从K列获取姓名是否正常工作
"""

import pandas as pd
from attendance_parser import AttendanceParser

def create_test_excel():
    """创建测试用的Excel文件"""
    # 创建测试数据
    data = []
    
    # 添加表头行
    header_row = [''] * 15
    header_row[0] = '工 号:'
    header_row[2] = '工号'
    header_row[10] = '姓名'  # K列
    data.append(header_row)
    
    # 添加员工1数据
    emp1_row = [''] * 15
    emp1_row[0] = '工 号:'
    emp1_row[2] = 'EMP001'
    emp1_row[10] = '张三'  # K列姓名
    data.append(emp1_row)
    
    # 添加员工1考勤数据
    attendance1_row = ['08:00 17:00', '08:30 17:30', '', '07:45 17:15', '08:15 17:00'] + [''] * 10
    data.append(attendance1_row)
    
    # 添加员工2数据
    emp2_row = [''] * 15
    emp2_row[0] = '工 号:'
    emp2_row[2] = 'EMP002'
    emp2_row[10] = '李四'  # K列姓名
    data.append(emp2_row)
    
    # 添加员工2考勤数据
    attendance2_row = ['', '08:00 17:00', '08:00 17:00', '', '08:00 17:00'] + [''] * 10
    data.append(attendance2_row)
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data)
    test_file = 'test_attendance_with_names.xlsx'
    df.to_excel(test_file, index=False, header=False)
    
    return test_file

def test_name_parsing():
    """测试姓名解析功能"""
    print("=== 测试姓名解析功能 ===")
    
    # 创建测试文件
    test_file = create_test_excel()
    print(f"已创建测试文件: {test_file}")
    
    # 解析考勤数据
    parser = AttendanceParser()
    result = parser.parse_excel(test_file, total_days=5)
    
    if result['success']:
        print(f"✅ 解析成功，共解析{len(result['employees'])}名员工")
        
        # 检查员工数据
        for emp_id, emp_data in result['employees'].items():
            print(f"\n员工工号: {emp_id}")
            print(f"员工姓名: {emp_data.get('name', '未获取到姓名')}")
            print(f"考勤天数: {emp_data['total_days']}")
            
            # 检查前3天的考勤记录
            print("前3天考勤记录:")
            for i, record in enumerate(emp_data['daily_records'][:3]):
                status = "有记录" if record['has_record'] else "无记录"
                print(f"  第{record['day']}天: {status} - {record.get('raw_data', '')}")
        
        # 验证结果
        employees = result['employees']
        
        # 验证员工1
        if 'EMP001' in employees:
            assert employees['EMP001']['name'] == '张三', f"员工EMP001姓名应为'张三'，实际为'{employees['EMP001']['name']}'"
            print("✅ 员工EMP001姓名解析正确")
        else:
            print("❌ 未找到员工EMP001")
        
        # 验证员工2
        if 'EMP002' in employees:
            assert employees['EMP002']['name'] == '李四', f"员工EMP002姓名应为'李四'，实际为'{employees['EMP002']['name']}'"
            print("✅ 员工EMP002姓名解析正确")
        else:
            print("❌ 未找到员工EMP002")
        
        print("\n✅ 所有测试通过！姓名解析功能正常工作")
        
    else:
        print(f"❌ 解析失败: {result['message']}")
    
    # 清理测试文件
    import os
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"已清理测试文件: {test_file}")

if __name__ == "__main__":
    test_name_parsing()
