# -*- coding: utf-8 -*-
"""
考勤工时计算图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from attendance_parser import AttendanceParser
from work_time_calculator import WorkTimeCalculator
from salary_calculator import SalaryCalculator
from excel_exporter import ExcelExporter
import os


class AttendanceGUI:
    """考勤工时计算图形界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("考勤工时计算系统")
        self.root.geometry("1200x800")

        # 初始化组件
        self.parser = AttendanceParser()
        self.calculator = WorkTimeCalculator()
        self.salary_calculator = SalaryCalculator()
        self.exporter = ExcelExporter()

        # 数据存储
        self.current_file = None
        self.salary_file = None
        self.calculation_results = None
        self.salary_results = None

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件操作", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(3, weight=1)

        # 考勤文件选择
        ttk.Button(file_frame, text="选择考勤文件", command=self.select_attendance_file).grid(row=0, column=0, padx=(0, 10))
        self.attendance_file_label = ttk.Label(file_frame, text="未选择考勤文件")
        self.attendance_file_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 工资表选择
        ttk.Button(file_frame, text="选择工资表", command=self.select_salary_file).grid(row=0, column=2, padx=(0, 10))
        self.salary_file_label = ttk.Label(file_frame, text="未选择工资表")
        self.salary_file_label.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(0, 20))

        # 计算按钮
        ttk.Button(file_frame, text="开始计算", command=self.calculate_worktime).grid(row=0, column=4, padx=(10, 0))

        # 参数设置区域
        param_frame = ttk.LabelFrame(main_frame, text="计算参数", padding="5")
        param_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(param_frame, text="考勤天数:").grid(row=0, column=0, padx=(0, 5))
        self.total_days_var = tk.StringVar(value="31")
        days_spinbox = ttk.Spinbox(param_frame, from_=1, to=31, textvariable=self.total_days_var, width=5)
        days_spinbox.grid(row=0, column=1, padx=(0, 10))

        ttk.Label(param_frame, text="天").grid(row=0, column=2, padx=(0, 20))

        ttk.Label(param_frame, text="当月假期天数:").grid(row=0, column=3, padx=(0, 5))
        self.holiday_days_var = tk.StringVar(value="0")
        holiday_spinbox = ttk.Spinbox(param_frame, from_=0, to=31, textvariable=self.holiday_days_var, width=5)
        holiday_spinbox.grid(row=0, column=4, padx=(0, 10))

        ttk.Label(param_frame, text="天").grid(row=0, column=5, padx=(0, 20))

        ttk.Label(param_frame, text="说明: 超过假期天数的缺勤将算作请假").grid(row=1, column=0, columnspan=6, sticky=tk.W, pady=(5, 0))

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(control_frame, text="查看异常记录", command=self.show_abnormal_records).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="计算工资", command=self.calculate_salary).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="导出Excel", command=self.export_excel).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="清空结果", command=self.clear_results).pack(side=tk.LEFT)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="计算结果", padding="5")
        result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

        # 创建Notebook用于分页显示
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 汇总页面
        self.create_summary_tab()

        # 详细页面
        self.create_detail_tab()

        # 工资页面
        self.create_salary_tab()

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def create_summary_tab(self):
        """创建汇总页面"""
        summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(summary_frame, text="工时汇总")

        # 创建树形视图
        columns = ('工号', '出勤天数', '正常工时', '加班工时', '总工时', '迟到次数', '早退次数', '请假次数', '违规总数', '是否满勤')
        self.summary_tree = ttk.Treeview(summary_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.summary_tree.heading(col, text=col)
            self.summary_tree.column(col, width=80, anchor=tk.CENTER)

        # 添加滚动条
        summary_scrollbar = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=self.summary_tree.yview)
        self.summary_tree.configure(yscrollcommand=summary_scrollbar.set)

        # 布局
        self.summary_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        summary_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        summary_frame.columnconfigure(0, weight=1)
        summary_frame.rowconfigure(0, weight=1)

    def create_detail_tab(self):
        """创建详细页面"""
        detail_frame = ttk.Frame(self.notebook)
        self.notebook.add(detail_frame, text="每日详情")

        # 员工选择
        select_frame = ttk.Frame(detail_frame)
        select_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(select_frame, text="选择员工:").pack(side=tk.LEFT, padx=(0, 10))
        self.employee_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(select_frame, textvariable=self.employee_var, state="readonly")
        self.employee_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.employee_combo.bind('<<ComboboxSelected>>', self.on_employee_selected)

        # 详细信息树形视图
        detail_columns = ('日期', '班次', '上班时间', '下班时间', '总工时', '正常工时', '加班工时', '迟到', '早退', '备注')
        self.detail_tree = ttk.Treeview(detail_frame, columns=detail_columns, show='headings', height=20)

        # 设置列标题
        for col in detail_columns:
            self.detail_tree.heading(col, text=col)
            self.detail_tree.column(col, width=80, anchor=tk.CENTER)

        # 添加滚动条
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_tree.yview)
        self.detail_tree.configure(yscrollcommand=detail_scrollbar.set)

        # 布局
        self.detail_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        detail_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        detail_frame.columnconfigure(0, weight=1)
        detail_frame.rowconfigure(1, weight=1)

    def create_salary_tab(self):
        """创建工资页面"""
        salary_frame = ttk.Frame(self.notebook)
        self.notebook.add(salary_frame, text="工资计算")

        # 创建树形视图
        salary_columns = ('工号', '姓名', '基础工资', '工时费', '出勤天数', '不满一天工时', '加班工时', '总工时', '基础工资计算', '满勤档位', '满勤奖', '最终工资', '工资差额')
        self.salary_tree = ttk.Treeview(salary_frame, columns=salary_columns, show='headings', height=20)

        # 设置列标题
        for col in salary_columns:
            self.salary_tree.heading(col, text=col)
            self.salary_tree.column(col, width=80, anchor=tk.CENTER)

        # 添加滚动条
        salary_scrollbar = ttk.Scrollbar(salary_frame, orient=tk.VERTICAL, command=self.salary_tree.yview)
        self.salary_tree.configure(yscrollcommand=salary_scrollbar.set)

        # 布局
        self.salary_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        salary_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        salary_frame.columnconfigure(0, weight=1)
        salary_frame.rowconfigure(0, weight=1)

    def select_attendance_file(self):
        """选择考勤文件"""
        file_path = filedialog.askopenfilename(
            title="选择考勤Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            self.current_file = file_path
            self.attendance_file_label.config(text=os.path.basename(file_path))
            self.status_var.set(f"已选择考勤文件: {os.path.basename(file_path)}")

    def select_salary_file(self):
        """选择工资表文件"""
        file_path = filedialog.askopenfilename(
            title="选择工资表Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            self.salary_file = file_path
            self.salary_file_label.config(text=os.path.basename(file_path))

            # 立即加载工资数据
            result = self.salary_calculator.load_salary_data(file_path)
            if result['success']:
                self.status_var.set(f"已加载工资表: {result['message']}")
            else:
                error_msg = str(result.get('message', '未知错误'))
                messagebox.showerror("加载失败", error_msg)
                self.salary_file = None
                self.salary_file_label.config(text="未选择工资表")

    def calculate_worktime(self):
        """计算工时"""
        if not self.current_file:
            messagebox.showerror("错误", "请先选择考勤文件")
            return

        try:
            self.status_var.set("正在解析考勤数据...")
            self.root.update()

            # 获取用户设置的天数
            try:
                total_days = int(self.total_days_var.get())
            except ValueError:
                total_days = 31

            # 解析考勤数据
            parse_result = self.parser.parse_excel(self.current_file, total_days)

            if not parse_result['success']:
                error_msg = str(parse_result.get('message', '未知错误'))
                messagebox.showerror("解析失败", error_msg)
                return

            # 检查是否有异常记录需要处理
            abnormal_records = self.parser.get_abnormal_records()
            single_punch_records = [r for r in abnormal_records if "只有一次打卡" in r['reason']]

            if single_punch_records:
                # 有只有一次打卡的记录，需要用户先处理
                self._show_single_punch_dialog(single_punch_records)
                return

            self.status_var.set("正在计算工时...")
            self.root.update()

            # 获取假期天数参数
            try:
                holiday_days = int(self.holiday_days_var.get())
            except ValueError:
                holiday_days = 0

            # 计算工时
            results = {}
            for emp_id, emp_data in parse_result['employees'].items():
                result = self.calculator.calculate_employee_worktime(emp_data, holiday_days)
                results[emp_id] = result

            self.calculation_results = {
                'total_days': parse_result['total_days'],
                'results': results
            }

            # 如果已选择工资表，自动计算工资
            if self.salary_file and self.salary_calculator.salary_data:
                self._calculate_salary_internal(results, total_days, holiday_days)

            # 更新界面显示
            self.update_summary_display()
            self.update_employee_list()

            self.status_var.set(f"计算完成，共处理{len(results)}名员工")

            if self.salary_results:
                messagebox.showinfo("成功", f"工时和工资计算完成！\n共处理{len(results)}名员工的考勤数据")
            else:
                messagebox.showinfo("成功", f"工时计算完成！\n共处理{len(results)}名员工的考勤数据\n提示：选择工资表可自动计算工资")

        except Exception as e:
            error_msg = f"计算过程中发生错误:\n{str(e)}"
            messagebox.showerror("计算失败", error_msg)
            self.status_var.set("计算失败")

    def calculate_salary(self):
        """计算工资"""
        if not self.calculation_results:
            messagebox.showwarning("提示", "请先计算工时")
            return

        if not self.salary_file:
            messagebox.showwarning("提示", "请先选择工资表")
            return

        if not self.salary_calculator.salary_data:
            messagebox.showerror("错误", "工资表数据未加载")
            return

        try:
            # 获取参数
            try:
                total_days = int(self.total_days_var.get())
                holiday_days = int(self.holiday_days_var.get())
            except ValueError:
                total_days = 31
                holiday_days = 0

            self._calculate_salary_internal(self.calculation_results['results'], total_days, holiday_days)
            messagebox.showinfo("成功", "工资计算完成！")
        except Exception as e:
            error_msg = f"工资计算过程中发生错误:\n{str(e)}"
            messagebox.showerror("计算失败", error_msg)

    def _calculate_salary_internal(self, worktime_results, total_days, holiday_days):
        """内部工资计算方法"""
        self.status_var.set("正在计算工资...")
        self.root.update()

        # 计算所有员工工资
        salary_result = self.salary_calculator.calculate_all_salaries(worktime_results, total_days, holiday_days)

        if salary_result['success']:
            self.salary_results = salary_result['salary_results']

            # 更新工资显示
            self.update_salary_display()

            # 显示缺失工资数据的员工
            if salary_result['missing_salary_data']:
                missing_list = ', '.join(str(emp_id) for emp_id in salary_result['missing_salary_data'])
                warning_msg = f"以下员工未找到工资数据，已跳过工资计算:\n{missing_list}"
                messagebox.showwarning("提示", warning_msg)

        self.status_var.set(f"工资计算完成，处理{salary_result['processed_count']}名员工")

    def update_salary_display(self):
        """更新工资显示"""
        # 清空现有数据
        for item in self.salary_tree.get_children():
            self.salary_tree.delete(item)

        if not self.salary_results:
            return

        # 添加数据
        for emp_id, result in self.salary_results.items():
            salary_diff = result['calculated_salary'] - result['base_salary']

            # 满勤档位显示
            tier = result['perfect_attendance_tier']
            tier_text = f"{tier}档" if tier > 0 else "无"

            values = (
                emp_id,
                result['employee_name'],
                f"{result['base_salary']:.2f}",
                f"{result['hourly_rate']:.2f}",
                f"{result['full_days']}天",
                f"{result['remaining_hours']:.1f}h",
                f"{result['total_overtime_hours']:.1f}h",
                f"{result['total_work_hours']:.1f}h",
                f"{result['base_calculated_salary']:.2f}",
                tier_text,
                f"{result['perfect_attendance_bonus']:.0f}",
                f"{result['calculated_salary']:.2f}",
                f"{salary_diff:+.2f}"
            )

            self.salary_tree.insert('', tk.END, values=values)

    def update_summary_display(self):
        """更新汇总显示"""
        # 清空现有数据
        for item in self.summary_tree.get_children():
            self.summary_tree.delete(item)

        if not self.calculation_results:
            return

        # 添加数据
        for emp_id, result in self.calculation_results['results'].items():
            summary = result['monthly_summary']

            values = (
                emp_id,
                summary['total_work_days'],
                f"{summary['total_normal_hours']:.1f}",
                f"{summary['total_overtime_hours']:.1f}",
                f"{summary['total_normal_hours'] + summary['total_overtime_hours']:.1f}",
                summary['late_count'],
                summary['early_leave_count'],
                summary['leave_count'],
                summary['total_violations'],
                "是" if summary['has_perfect_attendance'] else "否"
            )

            self.summary_tree.insert('', tk.END, values=values)

    def update_employee_list(self):
        """更新员工列表"""
        if not self.calculation_results:
            self.employee_combo['values'] = []
            return

        employee_list = list(self.calculation_results['results'].keys())
        self.employee_combo['values'] = employee_list

        if employee_list:
            self.employee_combo.set(employee_list[0])
            self.on_employee_selected(None)

    def on_employee_selected(self, event):
        """员工选择事件"""
        selected_emp = self.employee_var.get()
        if not selected_emp or not self.calculation_results:
            return

        # 清空详细信息
        for item in self.detail_tree.get_children():
            self.detail_tree.delete(item)

        # 添加员工详细信息
        result = self.calculation_results['results'][selected_emp]
        for daily in result['daily_results']:
            shift_text = ""
            if daily['shift_type'] == 'day':
                shift_text = "白班"
            elif daily['shift_type'] == 'night':
                shift_text = "夜班"

            late_text = f"{daily['late_minutes']}分钟" if daily['is_late'] else "否"
            early_text = f"{daily['early_leave_minutes']}分钟" if daily['is_early_leave'] else "否"

            remark = ""
            if not daily['is_present']:
                remark = "缺勤"
            elif daily.get('is_abnormal'):
                remark = daily.get('error_message', '异常')

            values = (
                f"{daily['day']}日",
                shift_text,
                daily['work_start'] or "-",
                daily['work_end'] or "-",
                f"{daily['total_hours']:.1f}h" if daily['total_hours'] > 0 else "-",
                f"{daily['normal_hours']:.1f}h" if daily['normal_hours'] > 0 else "-",
                f"{daily['overtime_hours']:.1f}h" if daily['overtime_hours'] > 0 else "-",
                late_text,
                early_text,
                remark
            )

            self.detail_tree.insert('', tk.END, values=values)

    def show_abnormal_records(self):
        """显示异常记录"""
        if not hasattr(self.parser, 'employees') or not self.parser.employees:
            messagebox.showinfo("提示", "请先计算工时")
            return

        abnormal_records = self.parser.get_abnormal_records()

        if not abnormal_records:
            messagebox.showinfo("提示", "没有发现异常记录")
            return

        # 创建异常记录窗口
        abnormal_window = tk.Toplevel(self.root)
        abnormal_window.title("异常记录")
        abnormal_window.geometry("800x400")

        # 创建树形视图
        columns = ('工号', '日期', '原始数据', '异常原因')
        abnormal_tree = ttk.Treeview(abnormal_window, columns=columns, show='headings')

        for col in columns:
            abnormal_tree.heading(col, text=col)
            abnormal_tree.column(col, width=150)

        # 添加数据
        for record in abnormal_records:
            values = (
                record['employee_id'],
                f"{record['day']}日",
                record['raw_data'],
                record['reason']
            )
            abnormal_tree.insert('', tk.END, values=values)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(abnormal_window, orient=tk.VERTICAL, command=abnormal_tree.yview)
        abnormal_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        abnormal_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def export_excel(self):
        """导出Excel"""
        if not self.calculation_results:
            messagebox.showinfo("提示", "请先计算工时")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存工时报表",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 准备导出数据
                export_data = {
                    'calculation_params': {
                        'total_days': self.calculation_results['total_days'],
                        'standard_hours': self.calculator.day_standard_hours,
                        'time_unit': self.calculator.time_unit
                    },
                    'results': {}
                }

                # 转换数据格式
                for emp_id, result in self.calculation_results['results'].items():
                    # 获取员工姓名，优先从考勤数据中获取
                    emp_name = emp_id  # 默认使用工号
                    if hasattr(self.parser, 'employees') and emp_id in self.parser.employees:
                        if 'name' in self.parser.employees[emp_id]:
                            emp_name = self.parser.employees[emp_id]['name']

                    # 如果考勤数据中没有姓名，再从工资数据中获取
                    if emp_name == emp_id and self.salary_results and emp_id in self.salary_results:
                        emp_name = self.salary_results[emp_id]['employee_name']

                    export_data['results'][emp_id] = {
                        'employee_data': self.parser.employees.get(emp_id, {}),  # 添加原始员工数据
                        'daily_results': result['daily_results'],
                        'monthly_summary': result['monthly_summary']  # 直接使用monthly_summary
                    }

                    # 如果有工资计算结果，添加工资数据
                    if self.salary_results and emp_id in self.salary_results:
                        export_data['results'][emp_id]['salary_calculation'] = self.salary_results[emp_id]

                # 导出
                success = self.exporter.export_worktime_report(export_data, file_path)

                if success:
                    messagebox.showinfo("成功", f"工时报表已导出到:\n{file_path}")
                else:
                    messagebox.showerror("失败", "导出失败")

            except Exception as e:
                error_msg = f"导出过程中发生错误:\n{str(e)}"
                messagebox.showerror("导出失败", error_msg)

    def _show_single_punch_dialog(self, single_punch_records):
        """
        显示单次打卡记录对话框，要求用户先处理

        Args:
            single_punch_records: 单次打卡记录列表
        """
        # 创建对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("发现异常打卡记录")
        dialog.geometry("800x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # 说明文字
        info_frame = ttk.Frame(dialog, padding="10")
        info_frame.pack(fill=tk.X)

        info_label = ttk.Label(info_frame, text="发现以下员工有只有一次打卡的记录，请先处理这些异常记录后再进行工时计算：",
                              font=("", 10, "bold"), foreground="red")
        info_label.pack(anchor=tk.W)

        # 异常记录列表
        list_frame = ttk.Frame(dialog, padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建树形视图
        columns = ('工号', '日期', '打卡数据', '问题描述')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.CENTER)

        # 添加数据
        for record in single_punch_records:
            values = (
                record['employee_id'],
                f"{record['day']}日",
                record['raw_data'],
                record['reason']
            )
            tree.insert('', tk.END, values=values)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮区域
        button_frame = ttk.Frame(dialog, padding="10")
        button_frame.pack(fill=tk.X)

        ttk.Label(button_frame, text="处理建议：请在Excel中补充缺失的打卡时间，或使用下方的手动修正功能。",
                 foreground="blue").pack(anchor=tk.W, pady=(0, 10))

        def close_dialog():
            dialog.destroy()
            self.status_var.set("请处理异常打卡记录后重新计算")

        def manual_fix():
            dialog.destroy()
            self._show_manual_fix_dialog(single_punch_records)

        def ignore_and_continue():
            # 忽略异常记录，继续计算
            dialog.destroy()
            self._continue_calculation_after_check()

        ttk.Button(button_frame, text="手动修正", command=manual_fix).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(button_frame, text="忽略并继续", command=ignore_and_continue).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(button_frame, text="取消计算", command=close_dialog).pack(side=tk.RIGHT)

    def _show_manual_fix_dialog(self, single_punch_records):
        """显示手动修正对话框"""
        # 创建修正对话框
        fix_dialog = tk.Toplevel(self.root)
        fix_dialog.title("手动修正异常打卡")
        fix_dialog.geometry("600x400")
        fix_dialog.transient(self.root)
        fix_dialog.grab_set()

        # 说明
        info_frame = ttk.Frame(fix_dialog, padding="10")
        info_frame.pack(fill=tk.X)
        ttk.Label(info_frame, text="请为以下异常记录补充上班和下班时间：", font=("", 10, "bold")).pack(anchor=tk.W)

        # 修正区域
        fix_frame = ttk.Frame(fix_dialog, padding="10")
        fix_frame.pack(fill=tk.BOTH, expand=True)

        self.fix_entries = {}  # 存储输入框

        for i, record in enumerate(single_punch_records):
            emp_id = record['employee_id']
            day = record['day']
            raw_data = record['raw_data']

            # 创建修正行
            row_frame = ttk.Frame(fix_frame)
            row_frame.pack(fill=tk.X, pady=5)

            ttk.Label(row_frame, text=f"工号{emp_id} 第{day}天:", width=15).pack(side=tk.LEFT)
            ttk.Label(row_frame, text=f"原始: {raw_data}", width=15, foreground="red").pack(side=tk.LEFT, padx=(0, 10))

            ttk.Label(row_frame, text="上班:").pack(side=tk.LEFT)
            start_entry = ttk.Entry(row_frame, width=8)
            start_entry.pack(side=tk.LEFT, padx=(0, 10))

            ttk.Label(row_frame, text="下班:").pack(side=tk.LEFT)
            end_entry = ttk.Entry(row_frame, width=8)
            end_entry.pack(side=tk.LEFT, padx=(0, 10))

            # 如果原始数据有时间，预填到上班时间
            if raw_data:
                start_entry.insert(0, raw_data)

            self.fix_entries[f"{emp_id}_{day}"] = {
                'start': start_entry,
                'end': end_entry,
                'employee_id': emp_id,
                'day': day
            }

        # 按钮区域
        button_frame = ttk.Frame(fix_dialog, padding="10")
        button_frame.pack(fill=tk.X)

        def apply_fixes():
            # 应用修正
            for key, entry_info in self.fix_entries.items():
                start_time = entry_info['start'].get().strip()
                end_time = entry_info['end'].get().strip()

                if start_time and end_time:
                    # 更新考勤记录
                    self.parser.update_record(
                        entry_info['employee_id'],
                        entry_info['day'],
                        start_time,
                        end_time
                    )

            fix_dialog.destroy()
            self._continue_calculation_after_check()

        def cancel_fix():
            fix_dialog.destroy()
            self.status_var.set("已取消修正")

        ttk.Button(button_frame, text="应用修正并计算", command=apply_fixes).pack(side=tk.RIGHT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=cancel_fix).pack(side=tk.RIGHT)

    def _continue_calculation_after_check(self):
        """异常检查后继续计算"""
        try:
            # 获取假期天数参数
            try:
                holiday_days = int(self.holiday_days_var.get())
            except ValueError:
                holiday_days = 0

            self.status_var.set("正在计算工时...")
            self.root.update()

            # 计算工时
            results = {}
            for emp_id, emp_data in self.parser.employees.items():
                result = self.calculator.calculate_employee_worktime(emp_data, holiday_days)
                results[emp_id] = result

            self.calculation_results = {
                'total_days': self.parser.total_days,
                'results': results
            }

            # 更新界面显示
            self.update_summary_display()
            self.update_employee_list()

            self.status_var.set(f"计算完成，共处理{len(results)}名员工")
            messagebox.showinfo("成功", f"工时计算完成！\n共处理{len(results)}名员工的考勤数据")

        except Exception as e:
            error_msg = f"计算过程中发生错误:\n{str(e)}"
            messagebox.showerror("计算失败", error_msg)
            self.status_var.set("计算失败")

    def clear_results(self):
        """清空结果"""
        self.calculation_results = None
        self.salary_results = None

        # 清空汇总表
        for item in self.summary_tree.get_children():
            self.summary_tree.delete(item)

        # 清空详细表
        for item in self.detail_tree.get_children():
            self.detail_tree.delete(item)

        # 清空工资表
        for item in self.salary_tree.get_children():
            self.salary_tree.delete(item)

        # 清空员工列表
        self.employee_combo['values'] = []
        self.employee_var.set('')

        self.status_var.set("已清空结果")

    def run(self):
        """运行程序"""
        self.root.mainloop()


if __name__ == "__main__":
    app = AttendanceGUI()
    app.run()
