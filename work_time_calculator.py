# -*- coding: utf-8 -*-
"""
工时计算引擎
根据考勤记录计算工时、加班、迟到早退等
"""

from datetime import datetime, time, timedelta
from typing import Dict, List, Tuple
import math


class WorkTimeCalculator:
    """工时计算器"""

    def __init__(self):
        # 白班时间定义
        self.day_shift_start = time(7, 30)      # 白班开始 7:30
        self.day_shift_end = time(17, 0)        # 白班结束 17:00
        self.morning_start = time(7, 30)        # 上午开始 7:30
        self.morning_end = time(11, 30)         # 上午结束 11:30
        self.afternoon_start = time(12, 30)     # 下午开始 12:30
        self.afternoon_end = time(17, 0)        # 下午结束 17:00

        # 夜班时间定义
        self.night_shift_start = time(17, 0)    # 夜班开始 17:00
        self.night_shift_end = time(1, 0)       # 夜班结束 次日01:00

        # 标准工时
        self.day_standard_hours = 8.5   # 白班标准工时8.5小时（含午休1小时）
        self.night_standard_hours = 8.5 # 夜班标准工时8.5小时（实际8小时，但按8.5计算）

        # 时间单位（分钟）
        self.time_unit = 15  # 15分钟为单位

        # 加班阈值（分钟）
        self.overtime_threshold = 30  # 超过30分钟才算加班

    def calculate_employee_worktime(self, employee_data: Dict, holiday_days: int = 0) -> Dict:
        """
        计算员工工时

        Args:
            employee_data: 员工考勤数据
            holiday_days: 当月假期天数

        Returns:
            工时计算结果
        """
        daily_results = []
        monthly_summary = {
            'total_work_days': 0,
            'total_normal_hours': 0.0,
            'total_overtime_hours': 0.0,
            'late_count': 0,
            'early_leave_count': 0,
            'leave_count': 0,  # 请假次数
            'absent_count': 0,
            'total_violations': 0,
            'has_perfect_attendance': False
        }

        # 计算每日工时
        absent_days = 0  # 缺勤天数计数

        for record in employee_data['daily_records']:
            if record['has_record'] and not record['is_abnormal']:
                daily_result = self._calculate_daily_worktime(record)
                daily_results.append(daily_result)

                # 累计统计
                if daily_result['is_present']:
                    monthly_summary['total_work_days'] += 1
                    monthly_summary['total_normal_hours'] += daily_result['normal_hours']
                    monthly_summary['total_overtime_hours'] += daily_result['overtime_hours']

                    if daily_result['is_late']:
                        monthly_summary['late_count'] += 1
                    if daily_result['is_early_leave']:
                        monthly_summary['early_leave_count'] += 1
                else:
                    absent_days += 1
            else:
                # 异常或无记录
                if record['has_record']:
                    daily_results.append(self._create_abnormal_daily_result(record))
                else:
                    daily_results.append(self._create_absent_daily_result(record))
                    absent_days += 1

        # 设置统计数据
        monthly_summary['leave_count'] = absent_days  # 请假次数（实际没打卡的天数）

        # 区分假期和缺勤
        if absent_days <= holiday_days:
            # 没打卡天数不超过假期天数，不算缺勤
            monthly_summary['absent_count'] = 0
        else:
            # 超出假期天数的部分才算缺勤
            monthly_summary['absent_count'] = absent_days - holiday_days

        # 计算违规总次数（迟到+早退+缺勤，假期不算违规）
        monthly_summary['total_violations'] = (
            monthly_summary['late_count'] +
            monthly_summary['early_leave_count'] +
            monthly_summary['absent_count']  # 只有超出假期的缺勤才算违规
        )

        # 判断是否满勤
        monthly_summary['has_perfect_attendance'] = monthly_summary['total_violations'] <= 3

        return {
            'employee_id': employee_data['id'],
            'daily_results': daily_results,
            'monthly_summary': monthly_summary
        }

    def _calculate_daily_worktime(self, record: Dict) -> Dict:
        """
        计算单日工时

        Args:
            record: 单日考勤记录

        Returns:
            单日工时结果
        """
        work_start_str = record['work_start']
        work_end_str = record['work_end']

        if not work_start_str or not work_end_str:
            return self._create_absent_daily_result(record)

        try:
            # 解析时间
            work_start = datetime.strptime(work_start_str, '%H:%M').time()
            work_end = datetime.strptime(work_end_str, '%H:%M').time()

            # 判断班次
            shift_type = self._determine_shift_type(work_start, work_end)

            # 分别计算正常工时和加班工时
            normal_hours = self._calculate_normal_hours(work_start, work_end, shift_type)
            overtime_hours = self._calculate_overtime_hours(work_start, work_end, shift_type)

            # 总工时
            work_hours = normal_hours + overtime_hours

            # 判断迟到早退
            is_late, late_minutes = self._check_late(work_start, shift_type)
            is_early_leave, early_leave_minutes = self._check_early_leave(work_end, shift_type)

            return {
                'day': record['day'],
                'is_present': True,
                'shift_type': shift_type,
                'work_start': work_start_str,
                'work_end': work_end_str,
                'total_hours': work_hours,
                'normal_hours': normal_hours,
                'overtime_hours': overtime_hours,
                'is_late': is_late,
                'late_minutes': late_minutes,
                'is_early_leave': is_early_leave,
                'early_leave_minutes': early_leave_minutes,
                'raw_data': record['raw_data']
            }

        except Exception as e:
            return self._create_abnormal_daily_result(record, str(e))

    def _determine_shift_type(self, work_start: time, work_end: time) -> str:
        """
        判断班次类型

        Args:
            work_start: 上班时间
            work_end: 下班时间

        Returns:
            班次类型: 'day' 或 'night'
        """
        # 如果下班时间小于上班时间，说明跨天了，是夜班
        if work_end < work_start:
            return 'night'

        # 如果上班时间在下午5点或之后，是夜班
        if work_start >= time(17, 0):
            return 'night'

        # 如果上班时间在下午2点之后且下班时间在晚上9点之后，可能是夜班
        if work_start >= time(14, 0) and work_end >= time(21, 0):
            return 'night'

        # 如果下班时间在凌晨6点之前，是夜班
        if work_end <= time(6, 0):
            return 'night'

        # 默认是白班
        return 'day'

    def _calculate_work_hours(self, work_start: time, work_end: time, shift_type: str) -> float:
        """
        计算工作小时数

        Args:
            work_start: 上班时间
            work_end: 下班时间
            shift_type: 班次类型

        Returns:
            工作小时数
        """
        if shift_type == 'day':
            return self._calculate_day_shift_hours(work_start, work_end)
        else:
            return self._calculate_night_shift_hours(work_start, work_end)

    def _calculate_day_shift_hours(self, work_start: time, work_end: time) -> float:
        """
        计算白班工时
        白班规则：7:30-11:30(4h) + 12:30-17:00(4.5h) = 8.5h
        """
        # 转换为datetime对象
        start_dt = datetime.combine(datetime.today(), work_start)
        end_dt = datetime.combine(datetime.today(), work_end)

        # 定义时间段
        morning_start_dt = datetime.combine(datetime.today(), self.morning_start)    # 7:30
        morning_end_dt = datetime.combine(datetime.today(), self.morning_end)        # 11:30
        afternoon_start_dt = datetime.combine(datetime.today(), self.afternoon_start) # 12:30
        afternoon_end_dt = datetime.combine(datetime.today(), self.afternoon_end)    # 17:00

        total_hours = 0.0

        # 计算上午工时 (7:30-11:30)
        if start_dt < morning_end_dt and end_dt > morning_start_dt:
            morning_actual_start = max(start_dt, morning_start_dt)
            morning_actual_end = min(end_dt, morning_end_dt)

            if morning_actual_start < morning_actual_end:
                morning_hours = (morning_actual_end - morning_actual_start).total_seconds() / 3600
                total_hours += morning_hours

        # 计算下午工时 (12:30-17:00)
        if start_dt < afternoon_end_dt and end_dt > afternoon_start_dt:
            afternoon_actual_start = max(start_dt, afternoon_start_dt)
            afternoon_actual_end = min(end_dt, afternoon_end_dt)

            if afternoon_actual_start < afternoon_actual_end:
                afternoon_hours = (afternoon_actual_end - afternoon_actual_start).total_seconds() / 3600
                total_hours += afternoon_hours

        # 计算加班工时 (17:00之后)
        if end_dt > afternoon_end_dt:
            overtime_start = max(start_dt, afternoon_end_dt)
            overtime_hours = (end_dt - overtime_start).total_seconds() / 3600
            total_hours += overtime_hours

        # 按15分钟单位取整
        return self._round_to_quarter_hour(total_hours)

    def _calculate_night_shift_hours(self, work_start: time, work_end: time) -> float:
        """
        计算夜班工时
        夜班规则：17:00-次日01:00，按8.5小时计算
        """
        # 转换为datetime对象进行计算
        start_dt = datetime.combine(datetime.today(), work_start)

        if work_end < work_start:
            # 夜班跨天
            end_dt = datetime.combine(datetime.today() + timedelta(days=1), work_end)
        else:
            end_dt = datetime.combine(datetime.today(), work_end)

        # 计算时间差
        time_diff = end_dt - start_dt
        hours = time_diff.total_seconds() / 3600

        # 按15分钟单位取整
        return self._round_to_quarter_hour(hours)

    def _round_to_quarter_hour(self, hours: float) -> float:
        """
        按15分钟单位取整

        Args:
            hours: 小时数

        Returns:
            取整后的小时数
        """
        # 转换为分钟
        minutes = hours * 60

        # 按15分钟取整
        rounded_minutes = round(minutes / self.time_unit) * self.time_unit

        # 转换回小时
        return rounded_minutes / 60

    def _calculate_normal_hours(self, work_start: time, work_end: time, shift_type: str) -> float:
        """
        计算正常工时（严格按照标准工作时间计算）

        Args:
            work_start: 上班时间
            work_end: 下班时间
            shift_type: 班次类型

        Returns:
            正常工时
        """
        if shift_type == 'day':
            return self._calculate_day_normal_hours(work_start, work_end)
        else:
            return self._calculate_night_normal_hours(work_start, work_end)

    def _calculate_overtime_hours(self, work_start: time, work_end: time, shift_type: str) -> float:
        """
        计算加班工时（只计算超出标准下班时间的部分）

        Args:
            work_start: 上班时间
            work_end: 下班时间
            shift_type: 班次类型

        Returns:
            加班工时
        """
        if shift_type == 'day':
            return self._calculate_day_overtime_hours(work_start, work_end)
        else:
            return self._calculate_night_overtime_hours(work_start, work_end)

    def _check_late(self, work_start: time, shift_type: str) -> Tuple[bool, int]:
        """
        检查是否迟到

        Args:
            work_start: 上班时间
            shift_type: 班次类型

        Returns:
            (是否迟到, 迟到分钟数)
        """
        if shift_type == 'day':
            standard_start = self.day_shift_start
        else:
            standard_start = self.night_shift_start

        # 计算迟到分钟数
        start_dt = datetime.combine(datetime.today(), work_start)
        standard_dt = datetime.combine(datetime.today(), standard_start)

        if start_dt > standard_dt:
            late_minutes = int((start_dt - standard_dt).total_seconds() / 60)
            return True, late_minutes

        return False, 0

    def _check_early_leave(self, work_end: time, shift_type: str) -> Tuple[bool, int]:
        """
        检查是否早退

        Args:
            work_end: 下班时间
            shift_type: 班次类型

        Returns:
            (是否早退, 早退分钟数)
        """
        if shift_type == 'day':
            # 白班早退判定：下班时间早于17:00
            standard_end = self.day_shift_end  # 17:00
            end_dt = datetime.combine(datetime.today(), work_end)
            standard_dt = datetime.combine(datetime.today(), standard_end)

            if end_dt < standard_dt:
                early_minutes = int((standard_dt - end_dt).total_seconds() / 60)
                return True, early_minutes
        else:
            # 夜班早退判定：下班时间早于次日01:00
            standard_end = self.night_shift_end  # 01:00

            # 处理夜班跨天情况
            if work_end <= time(6, 0):  # 凌晨时间，认为是次日
                end_dt = datetime.combine(datetime.today() + timedelta(days=1), work_end)
                standard_dt = datetime.combine(datetime.today() + timedelta(days=1), standard_end)
            else:
                # 当天晚上时间，不应该早于次日01:00
                # 如果是当天晚上下班，肯定是早退
                return True, 0  # 具体早退时间计算复杂，暂时返回0

            if end_dt < standard_dt:
                early_minutes = int((standard_dt - end_dt).total_seconds() / 60)
                return True, early_minutes

        return False, 0

    def _create_absent_daily_result(self, record: Dict) -> Dict:
        """创建缺勤日结果"""
        return {
            'day': record['day'],
            'is_present': False,
            'shift_type': None,
            'work_start': None,
            'work_end': None,
            'total_hours': 0.0,
            'normal_hours': 0.0,
            'overtime_hours': 0.0,
            'is_late': False,
            'late_minutes': 0,
            'is_early_leave': False,
            'early_leave_minutes': 0,
            'raw_data': record.get('raw_data', '')
        }

    def _create_abnormal_daily_result(self, record: Dict, error_msg: str = '') -> Dict:
        """创建异常日结果"""
        result = self._create_absent_daily_result(record)
        result['is_abnormal'] = True
        result['error_message'] = error_msg or record.get('abnormal_reason', '')
        return result

    def _calculate_day_normal_hours(self, work_start: time, work_end: time) -> float:
        """
        计算白班正常工时
        白班标准时间：7:30-17:00 (上午7:30-11:30, 下午12:30-17:00)
        """
        total_hours = 0.0

        # 转换为datetime对象
        start_dt = datetime.combine(datetime.today(), work_start)
        end_dt = datetime.combine(datetime.today(), work_end)

        # 标准时间段
        morning_start_dt = datetime.combine(datetime.today(), self.morning_start)  # 7:30
        morning_end_dt = datetime.combine(datetime.today(), self.morning_end)      # 11:30
        afternoon_start_dt = datetime.combine(datetime.today(), self.afternoon_start)  # 12:30
        afternoon_end_dt = datetime.combine(datetime.today(), self.afternoon_end)      # 17:00

        # 计算上午正常工时 (7:30-11:30)
        if start_dt < morning_end_dt and end_dt > morning_start_dt:
            morning_actual_start = max(start_dt, morning_start_dt)
            morning_actual_end = min(end_dt, morning_end_dt)

            if morning_actual_start < morning_actual_end:
                morning_hours = (morning_actual_end - morning_actual_start).total_seconds() / 3600
                total_hours += morning_hours

        # 计算下午正常工时 (12:30-17:00)
        if start_dt < afternoon_end_dt and end_dt > afternoon_start_dt:
            afternoon_actual_start = max(start_dt, afternoon_start_dt)
            afternoon_actual_end = min(end_dt, afternoon_end_dt)

            if afternoon_actual_start < afternoon_actual_end:
                afternoon_hours = (afternoon_actual_end - afternoon_actual_start).total_seconds() / 3600
                total_hours += afternoon_hours

        # 按15分钟单位取整
        return self._round_to_quarter_hour(total_hours)

    def _calculate_day_overtime_hours(self, work_start: time, work_end: time) -> float:
        """
        计算白班加班工时
        只计算17:00之后的时间
        """
        # 转换为datetime对象
        start_dt = datetime.combine(datetime.today(), work_start)
        end_dt = datetime.combine(datetime.today(), work_end)
        afternoon_end_dt = datetime.combine(datetime.today(), self.afternoon_end)  # 17:00

        # 只有下班时间超过17:00才有加班
        if end_dt <= afternoon_end_dt:
            return 0.0

        # 加班开始时间是17:00或实际上班时间（如果上班时间晚于17:00）
        overtime_start = max(start_dt, afternoon_end_dt)
        overtime_hours = (end_dt - overtime_start).total_seconds() / 3600

        # 加班时间必须超过阈值才计算（30分钟）
        if overtime_hours * 60 < self.overtime_threshold:
            return 0.0

        # 按15分钟单位取整
        return self._round_to_quarter_hour(overtime_hours)

    def _calculate_night_normal_hours(self, work_start: time, work_end: time) -> float:
        """
        计算夜班正常工时
        夜班标准时间：17:00-次日01:00，按8.5小时计算
        """
        # 转换为datetime对象
        start_dt = datetime.combine(datetime.today(), work_start)

        if work_end < work_start:
            # 夜班跨天
            end_dt = datetime.combine(datetime.today() + timedelta(days=1), work_end)
        else:
            end_dt = datetime.combine(datetime.today(), work_end)

        # 夜班标准时间段：17:00-次日01:00
        night_start_dt = datetime.combine(datetime.today(), self.night_shift_start)  # 17:00
        night_end_dt = datetime.combine(datetime.today() + timedelta(days=1), self.night_shift_end)  # 次日01:00

        # 计算在标准夜班时间内的工时
        actual_start = max(start_dt, night_start_dt)
        actual_end = min(end_dt, night_end_dt)

        if actual_start < actual_end:
            hours = (actual_end - actual_start).total_seconds() / 3600
            return self._round_to_quarter_hour(hours)

        return 0.0

    def _calculate_night_overtime_hours(self, work_start: time, work_end: time) -> float:
        """
        计算夜班加班工时
        超过次日01:00的部分算加班
        """
        # 转换为datetime对象
        start_dt = datetime.combine(datetime.today(), work_start)

        if work_end < work_start:
            # 夜班跨天
            end_dt = datetime.combine(datetime.today() + timedelta(days=1), work_end)
        else:
            end_dt = datetime.combine(datetime.today(), work_end)

        # 夜班标准结束时间：次日01:00
        night_end_dt = datetime.combine(datetime.today() + timedelta(days=1), self.night_shift_end)

        # 只有下班时间超过次日01:00才有加班
        if end_dt <= night_end_dt:
            return 0.0

        # 加班开始时间是次日01:00或实际上班时间（如果上班时间晚于次日01:00）
        overtime_start = max(start_dt, night_end_dt)
        overtime_hours = (end_dt - overtime_start).total_seconds() / 3600

        # 加班时间必须超过阈值才计算（30分钟）
        if overtime_hours * 60 < self.overtime_threshold:
            return 0.0

        # 按15分钟单位取整
        return self._round_to_quarter_hour(overtime_hours)
