@echo off
chcp 65001 >nul
echo 开始打包考勤工时计算系统...
echo.

REM 清理之前的构建文件
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo 执行PyInstaller打包...
pyinstaller --onefile --windowed --name="考勤工时计算系统" --distpath=dist --workpath=build --clean --noconfirm --hidden-import=pandas --hidden-import=numpy --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.ttk --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox main.py

if %errorlevel% equ 0 (
    echo.
    echo 打包成功！
    echo 可执行文件位置: dist\考勤工时计算系统.exe
    
    REM 显示文件大小
    for %%A in (dist\考勤工时计算系统.exe) do (
        set /a size=%%~zA/1024/1024
        echo 文件大小: !size! MB
    )
    
    echo.
    set /p cleanup="是否清理构建临时文件？(y/n): "
    if /i "!cleanup!"=="y" (
        if exist build rmdir /s /q build
        if exist *.spec del *.spec
        echo 构建文件已清理
    )
) else (
    echo.
    echo 打包失败，请检查错误信息
)

pause
