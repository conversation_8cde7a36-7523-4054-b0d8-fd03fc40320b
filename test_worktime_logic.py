# -*- coding: utf-8 -*-
"""
测试新的工时计算逻辑
验证迟到和加班分开计算是否正确
"""

from work_time_calculator import WorkTimeCalculator

def test_worktime_separation():
    """测试工时分离计算逻辑"""
    calculator = WorkTimeCalculator()
    
    print("=== 测试工时分离计算逻辑 ===")
    print("标准白班时间：7:30-17:00 (上午7:30-11:30, 下午12:30-17:00)")
    print("标准工时：8.5小时")
    print()
    
    # 测试场景1：正常上下班
    print("场景1：正常上下班 (7:30-17:00)")
    employee_data = {
        'id': 'TEST001',
        'daily_records': [{
            'day': 1,
            'has_record': True,
            'is_abnormal': False,
            'work_start': '07:30',
            'work_end': '17:00',
            'raw_data': '07:30 17:00'
        }]
    }
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print(f"  是否迟到: {'是' if daily['is_late'] else '否'}")
    print()
    
    # 测试场景2：迟到1小时，正常下班
    print("场景2：迟到1小时，正常下班 (8:30-17:00)")
    employee_data['daily_records'][0]['work_start'] = '08:30'
    employee_data['daily_records'][0]['work_end'] = '17:00'
    employee_data['daily_records'][0]['raw_data'] = '08:30 17:00'
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print(f"  是否迟到: {'是' if daily['is_late'] else '否'}")
    print(f"  迟到分钟: {daily['late_minutes']}分钟")
    print()
    
    # 测试场景3：正常上班，加班3小时
    print("场景3：正常上班，加班3小时 (7:30-20:00)")
    employee_data['daily_records'][0]['work_start'] = '07:30'
    employee_data['daily_records'][0]['work_end'] = '20:00'
    employee_data['daily_records'][0]['raw_data'] = '07:30 20:00'
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print(f"  是否迟到: {'是' if daily['is_late'] else '否'}")
    print()
    
    # 测试场景4：迟到1小时，加班3小时（您提到的问题场景）
    print("场景4：迟到1小时，加班3小时 (8:30-20:00)")
    employee_data['daily_records'][0]['work_start'] = '08:30'
    employee_data['daily_records'][0]['work_end'] = '20:00'
    employee_data['daily_records'][0]['raw_data'] = '08:30 20:00'
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print(f"  是否迟到: {'是' if daily['is_late'] else '否'}")
    print(f"  迟到分钟: {daily['late_minutes']}分钟")
    print()
    
    # 测试场景5：早退1小时
    print("场景5：正常上班，早退1小时 (7:30-16:00)")
    employee_data['daily_records'][0]['work_start'] = '07:30'
    employee_data['daily_records'][0]['work_end'] = '16:00'
    employee_data['daily_records'][0]['raw_data'] = '07:30 16:00'
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print(f"  是否早退: {'是' if daily['is_early_leave'] else '否'}")
    print(f"  早退分钟: {daily['early_leave_minutes']}分钟")
    print()
    
    # 测试场景6：加班时间不足30分钟
    print("场景6：加班20分钟 (7:30-17:20)")
    employee_data['daily_records'][0]['work_start'] = '07:30'
    employee_data['daily_records'][0]['work_end'] = '17:20'
    employee_data['daily_records'][0]['raw_data'] = '07:30 17:20'
    
    result = calculator.calculate_employee_worktime(employee_data, holiday_days=0)
    daily = result['daily_results'][0]
    print(f"  正常工时: {daily['normal_hours']:.1f}小时")
    print(f"  加班工时: {daily['overtime_hours']:.1f}小时")
    print(f"  总工时: {daily['total_hours']:.1f}小时")
    print("  说明：加班不足30分钟，不计算加班工时")
    print()
    
    print("✅ 测试完成！新的工时计算逻辑：")
    print("  - 正常工时严格按照标准工作时间计算")
    print("  - 加班工时只计算超出标准下班时间的部分")
    print("  - 迟到不影响加班工时计算")
    print("  - 加班时间不足30分钟不计算")

if __name__ == "__main__":
    test_worktime_separation()
