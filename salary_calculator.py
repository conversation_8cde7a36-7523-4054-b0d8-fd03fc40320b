# -*- coding: utf-8 -*-
"""
工资计算器
根据工时和基础工资计算实际工资
"""

from typing import Dict, List
import pandas as pd


class SalaryCalculator:
    """工资计算器"""

    def __init__(self):
        self.salary_data = {}  # 员工工资数据 {工号: {'name': 姓名, 'base_salary': 基础工资}}
        self.monthly_days = 30  # 固定月天数
        self.standard_daily_hours = 8.5  # 标准日工时

        # 满勤奖档位设置
        self.perfect_attendance_bonus = {
            'tier1': 200,  # 一档满勤奖
            'tier2': 300,  # 二档满勤奖
            'tier3': 500   # 三档满勤奖
        }

    def load_salary_data(self, file_path: str) -> Dict:
        """
        加载工资表数据

        Args:
            file_path: 工资表Excel文件路径

        Returns:
            加载结果
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 清空现有数据
            self.salary_data = {}

            # 解析工资数据
            for index, row in df.iterrows():
                # 假设Excel格式：第一列工号，第二列姓名，第三列工资
                if len(row) >= 3:
                    emp_id = str(row.iloc[0]).strip()
                    name = str(row.iloc[1]).strip()
                    try:
                        base_salary = float(row.iloc[2])

                        if emp_id and emp_id != 'nan' and base_salary > 0:
                            self.salary_data[emp_id] = {
                                'name': name,
                                'base_salary': base_salary
                            }
                    except (ValueError, TypeError):
                        continue

            return {
                'success': True,
                'count': len(self.salary_data),
                'message': f'成功加载{len(self.salary_data)}名员工的工资数据'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'加载工资数据失败: {str(e)}'
            }

    def calculate_hourly_rate(self, base_salary: float) -> float:
        """
        计算工时费

        Args:
            base_salary: 基础工资

        Returns:
            工时费（保留两位小数）
        """
        hourly_rate = base_salary / self.monthly_days / self.standard_daily_hours
        return round(hourly_rate, 2)

    def calculate_perfect_attendance_tier(self, total_work_hours: float, total_days: int, holiday_days: int) -> Dict:
        """
        计算满勤奖档位

        Args:
            total_work_hours: 总工时
            total_days: 当月总天数
            holiday_days: 假期天数

        Returns:
            满勤奖信息
        """
        # 计算各档位的工时要求
        work_days = total_days - holiday_days
        tier1_hours = work_days * self.standard_daily_hours  # 一档：基础工时
        tier2_hours = tier1_hours + 30  # 二档：基础工时+30h
        tier3_hours = tier1_hours + 60  # 三档：基础工时+60h

        # 判断档位
        if total_work_hours >= tier3_hours:
            tier = 3
            bonus = self.perfect_attendance_bonus['tier3']
        elif total_work_hours >= tier2_hours:
            tier = 2
            bonus = self.perfect_attendance_bonus['tier2']
        elif total_work_hours >= tier1_hours:
            tier = 1
            bonus = self.perfect_attendance_bonus['tier1']
        else:
            tier = 0
            bonus = 0

        return {
            'tier': tier,
            'bonus': bonus,
            'tier1_hours': tier1_hours,
            'tier2_hours': tier2_hours,
            'tier3_hours': tier3_hours,
            'work_days': work_days
        }

    def calculate_employee_salary(self, emp_id: str, worktime_result: Dict, total_days: int = 31, holiday_days: int = 0) -> Dict:
        """
        计算员工工资

        Args:
            emp_id: 员工工号
            worktime_result: 工时计算结果
            total_days: 当月总天数
            holiday_days: 假期天数

        Returns:
            工资计算结果
        """
        if emp_id not in self.salary_data:
            return {
                'success': False,
                'error': f'未找到工号{emp_id}的工资数据'
            }

        salary_info = self.salary_data[emp_id]
        base_salary = salary_info['base_salary']
        hourly_rate = self.calculate_hourly_rate(base_salary)

        # 获取工时数据
        monthly_summary = worktime_result['monthly_summary']
        total_normal_hours = monthly_summary['total_normal_hours']
        total_overtime_hours = monthly_summary['total_overtime_hours']

        # 计算出勤天数和不满一天的工时
        full_days = int(total_normal_hours // self.standard_daily_hours)
        remaining_hours = total_normal_hours % self.standard_daily_hours

        # 计算工资：（出勤天数*8.5h + 不满一天的工时 + 加班时间）* 工时费
        total_work_hours = (full_days * self.standard_daily_hours +
                           remaining_hours +
                           total_overtime_hours)

        base_calculated_salary = total_work_hours * hourly_rate

        # 计算满勤奖档位（只有满勤的员工才能获得满勤奖）
        perfect_attendance_info = {'tier': 0, 'bonus': 0}
        if monthly_summary.get('has_perfect_attendance', False):
            perfect_attendance_info = self.calculate_perfect_attendance_tier(
                total_work_hours, total_days, holiday_days
            )

        # 最终工资 = 基础计算工资 + 满勤奖
        final_salary = base_calculated_salary + perfect_attendance_info['bonus']

        return {
            'success': True,
            'employee_id': emp_id,
            'employee_name': salary_info['name'],
            'base_salary': base_salary,
            'hourly_rate': hourly_rate,
            'full_days': full_days,
            'remaining_hours': round(remaining_hours, 1),
            'total_normal_hours': round(total_normal_hours, 1),
            'total_overtime_hours': round(total_overtime_hours, 1),
            'total_work_hours': round(total_work_hours, 1),
            'base_calculated_salary': round(base_calculated_salary, 2),
            'perfect_attendance_tier': perfect_attendance_info['tier'],
            'perfect_attendance_bonus': perfect_attendance_info['bonus'],
            'calculated_salary': round(final_salary, 2),
            'monthly_summary': monthly_summary,
            'perfect_attendance_info': perfect_attendance_info
        }

    def calculate_all_salaries(self, worktime_results: Dict, total_days: int = 31, holiday_days: int = 0) -> Dict:
        """
        计算所有员工工资

        Args:
            worktime_results: 所有员工的工时计算结果

        Returns:
            所有员工的工资计算结果
        """
        salary_results = {}
        missing_salary_data = []

        for emp_id, worktime_result in worktime_results.items():
            salary_result = self.calculate_employee_salary(emp_id, worktime_result, total_days, holiday_days)

            if salary_result['success']:
                salary_results[emp_id] = salary_result
            else:
                missing_salary_data.append(emp_id)

        return {
            'success': True,
            'salary_results': salary_results,
            'missing_salary_data': missing_salary_data,
            'processed_count': len(salary_results),
            'missing_count': len(missing_salary_data)
        }

    def get_salary_summary(self, salary_results: Dict) -> Dict:
        """
        获取工资汇总信息

        Args:
            salary_results: 工资计算结果

        Returns:
            汇总信息
        """
        if not salary_results:
            return {
                'total_employees': 0,
                'total_base_salary': 0.0,
                'total_calculated_salary': 0.0,
                'average_hourly_rate': 0.0
            }

        total_base_salary = sum(result['base_salary'] for result in salary_results.values())
        total_calculated_salary = sum(result['calculated_salary'] for result in salary_results.values())
        average_hourly_rate = sum(result['hourly_rate'] for result in salary_results.values()) / len(salary_results)

        return {
            'total_employees': len(salary_results),
            'total_base_salary': round(total_base_salary, 2),
            'total_calculated_salary': round(total_calculated_salary, 2),
            'average_hourly_rate': round(average_hourly_rate, 2),
            'salary_difference': round(total_calculated_salary - total_base_salary, 2)
        }

    def get_loaded_employees(self) -> List[Dict]:
        """
        获取已加载的员工工资信息

        Returns:
            员工工资信息列表
        """
        return [
            {
                'emp_id': emp_id,
                'name': info['name'],
                'base_salary': info['base_salary'],
                'hourly_rate': self.calculate_hourly_rate(info['base_salary'])
            }
            for emp_id, info in self.salary_data.items()
        ]
