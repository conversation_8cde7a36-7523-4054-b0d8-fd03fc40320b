# -*- coding: utf-8 -*-
"""
考勤数据解析器
处理Excel考勤表，解析打卡记录
"""

import pandas as pd
import re
from datetime import datetime, time
from typing import Dict, List, Tuple, Optional
import numpy as np


class AttendanceParser:
    """考勤数据解析器"""

    def __init__(self):
        self.employees = {}  # 员工考勤数据
        self.total_days = 0  # 总天数

    def parse_excel(self, file_path: str, total_days: int = 31) -> Dict:
        """
        解析Excel考勤文件

        Args:
            file_path: Excel文件路径
            total_days: 考勤总天数

        Returns:
            解析结果字典
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 使用用户指定的天数
            self.total_days = total_days

            # 解析员工考勤数据
            self.employees = self._parse_employee_data(df)

            return {
                'success': True,
                'total_days': self.total_days,
                'employees': self.employees,
                'message': f'成功解析{len(self.employees)}名员工的考勤数据'
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'解析失败: {str(e)}'
            }

    def _get_total_days(self, df: pd.DataFrame) -> int:
        """
        从表头获取总天数

        Args:
            df: 考勤数据DataFrame

        Returns:
            总天数
        """
        # 查找日期行（通常是第3行，包含1,2,3...的数字）
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            # 统计数字列的数量
            numeric_count = 0
            for val in row:
                if pd.notna(val) and str(val).isdigit():
                    numeric_count += 1

            if numeric_count >= 28:  # 至少28天
                return numeric_count

        # 默认返回31天
        return 31

    def _parse_employee_data(self, df: pd.DataFrame) -> Dict:
        """
        解析员工考勤数据

        Args:
            df: 考勤数据DataFrame

        Returns:
            员工考勤数据字典
        """
        employees = {}

        # 查找所有工号行
        for i in range(len(df)):
            if pd.notna(df.iloc[i, 0]) and '工 号:' in str(df.iloc[i, 0]):
                # 获取工号
                employee_id = str(df.iloc[i, 2]).strip()
                if employee_id == 'nan':
                    continue

                # 获取姓名（K列，索引为10）
                employee_name = ''
                if len(df.columns) > 10 and pd.notna(df.iloc[i, 10]):
                    employee_name = str(df.iloc[i, 10]).strip()

                # 获取考勤数据行（工号行的下一行）
                if i + 1 < len(df):
                    attendance_row = df.iloc[i + 1]
                    daily_records = self._parse_daily_records(attendance_row)

                    employees[employee_id] = {
                        'id': employee_id,
                        'name': employee_name,
                        'daily_records': daily_records,
                        'total_days': len([r for r in daily_records if r['has_record']])
                    }

        return employees

    def _parse_daily_records(self, row: pd.Series) -> List[Dict]:
        """
        解析每日考勤记录

        Args:
            row: 考勤数据行

        Returns:
            每日记录列表
        """
        daily_records = []

        # 根据Excel结构：A列(索引0)=第1天，B列(索引1)=第2天，以此类推
        for day in range(1, self.total_days + 1):
            col_index = day - 1  # day=1对应A列(索引0)，day=2对应B列(索引1)

            if col_index < len(row):
                raw_data = row.iloc[col_index]
                record = self._parse_single_day_record(day, raw_data)
            else:
                record = self._create_empty_record(day)

            daily_records.append(record)

        return daily_records

    def _parse_single_day_record(self, day: int, raw_data) -> Dict:
        """
        解析单日考勤记录

        Args:
            day: 日期
            raw_data: 原始打卡数据

        Returns:
            单日记录字典
        """
        if pd.isna(raw_data) or raw_data == '':
            return self._create_empty_record(day)

        raw_str = str(raw_data).strip()

        # 解析打卡时间
        punch_times = self._extract_punch_times(raw_str)

        if not punch_times:
            return {
                'day': day,
                'has_record': False,
                'raw_data': raw_str,
                'punch_times': [],
                'work_start': None,
                'work_end': None,
                'is_abnormal': True,
                'abnormal_reason': '无法解析打卡时间'
            }

        # 处理打卡时间
        processed_times = self._process_punch_times(punch_times)

        return {
            'day': day,
            'has_record': True,
            'raw_data': raw_str,
            'punch_times': punch_times,
            'work_start': processed_times['work_start'],
            'work_end': processed_times['work_end'],
            'is_abnormal': processed_times['is_abnormal'],
            'abnormal_reason': processed_times.get('abnormal_reason', '')
        }

    def _create_empty_record(self, day: int) -> Dict:
        """创建空记录"""
        return {
            'day': day,
            'has_record': False,
            'raw_data': '',
            'punch_times': [],
            'work_start': None,
            'work_end': None,
            'is_abnormal': False,
            'abnormal_reason': ''
        }

    def _extract_punch_times(self, raw_str: str) -> List[str]:
        """
        从原始字符串中提取打卡时间

        Args:
            raw_str: 原始打卡字符串

        Returns:
            打卡时间列表
        """
        # 匹配时间格式：HH:MM
        time_pattern = r'\d{2}:\d{2}'
        times = re.findall(time_pattern, raw_str)

        # 不去重，保持原始顺序，因为可能有重复打卡的情况
        # 但需要排序以便后续处理
        times.sort()

        return times

    def _process_punch_times(self, punch_times: List[str]) -> Dict:
        """
        处理打卡时间，确定上下班时间

        Args:
            punch_times: 打卡时间列表

        Returns:
            处理结果
        """
        if len(punch_times) == 0:
            return {
                'work_start': None,
                'work_end': None,
                'is_abnormal': True,
                'abnormal_reason': '无打卡记录'
            }

        elif len(punch_times) == 1:
            return {
                'work_start': None,
                'work_end': None,
                'is_abnormal': True,
                'abnormal_reason': f'只有一次打卡: {punch_times[0]}'
            }

        elif len(punch_times) == 2:
            # 正常情况：上班和下班
            return {
                'work_start': punch_times[0],
                'work_end': punch_times[1],
                'is_abnormal': False
            }

        elif len(punch_times) == 3:
            # 三次打卡，可能是夜班跨天情况
            # 判断是否有凌晨时间（夜班下班）
            early_morning_times = [t for t in punch_times if self._is_early_morning(t)]

            if early_morning_times:
                # 有凌晨时间，可能是夜班跨天
                # 凌晨时间是前一天夜班的下班时间，不算在当天
                # 取剩余的两个时间作为当天的上下班时间
                day_times = [t for t in punch_times if not self._is_early_morning(t)]
                if len(day_times) >= 2:
                    return {
                        'work_start': day_times[0],
                        'work_end': day_times[-1],
                        'is_abnormal': False,
                        'abnormal_reason': f'夜班跨天处理: {", ".join(punch_times)}'
                    }

            # 普通三次打卡：上班取最早，下班取最晚
            return {
                'work_start': punch_times[0],
                'work_end': punch_times[-1],
                'is_abnormal': False,
                'abnormal_reason': f'多次打卡已自动处理: {", ".join(punch_times)}'
            }

        else:
            # 四次或更多打卡
            # 检查是否有凌晨时间（夜班跨天）
            early_morning_times = [t for t in punch_times if self._is_early_morning(t)]

            if early_morning_times:
                # 有凌晨时间，过滤掉凌晨时间后处理剩余的打卡
                day_times = [t for t in punch_times if not self._is_early_morning(t)]
                if len(day_times) >= 2:
                    return {
                        'work_start': day_times[0],
                        'work_end': day_times[-1],
                        'is_abnormal': False,
                        'abnormal_reason': f'夜班跨天多次打卡处理: {", ".join(punch_times)}'
                    }

            # 普通多次打卡：上班取最早，下班取最晚
            return {
                'work_start': punch_times[0],
                'work_end': punch_times[-1],
                'is_abnormal': False,
                'abnormal_reason': f'多次打卡已自动处理: {", ".join(punch_times)}'
            }

    def _is_early_morning(self, time_str: str) -> bool:
        """
        判断是否为凌晨时间（00:00-06:00）

        Args:
            time_str: 时间字符串，格式为HH:MM

        Returns:
            是否为凌晨时间
        """
        try:
            hour = int(time_str.split(':')[0])
            return 0 <= hour <= 6
        except:
            return False

    def get_abnormal_records(self) -> List[Dict]:
        """
        获取所有异常记录

        Returns:
            异常记录列表
        """
        abnormal_records = []

        for emp_id, emp_data in self.employees.items():
            for record in emp_data['daily_records']:
                if record['is_abnormal']:
                    abnormal_records.append({
                        'employee_id': emp_id,
                        'day': record['day'],
                        'raw_data': record['raw_data'],
                        'reason': record['abnormal_reason']
                    })

        return abnormal_records

    def update_record(self, employee_id: str, day: int, work_start: str, work_end: str):
        """
        更新考勤记录

        Args:
            employee_id: 员工ID
            day: 日期
            work_start: 上班时间
            work_end: 下班时间
        """
        if employee_id in self.employees:
            for record in self.employees[employee_id]['daily_records']:
                if record['day'] == day:
                    record['work_start'] = work_start
                    record['work_end'] = work_end
                    record['is_abnormal'] = False
                    record['abnormal_reason'] = '手动修正'
                    break
